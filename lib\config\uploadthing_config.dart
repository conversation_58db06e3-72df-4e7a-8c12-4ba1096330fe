class UploadThingConfig {
  // Replace with your actual UploadThing app ID and secret
  // Get these from https://uploadthing.com/dashboard
  static const String appId = 'your_uploadthing_app_id';
  static const String secret = 'sk_live_your_secret_key_here';

  // API endpoints
  static const String baseUrl = 'https://api.uploadthing.com';
  static const String uploadEndpoint = '/api/uploadthing';

  // Upload settings
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB in bytes
  static const List<String> allowedFileTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/jpg',
  ];
  static const int maxFiles = 5;

  // Image optimization settings
  static const int maxImageWidth = 1024;
  static const int maxImageHeight = 1024;
  static const int imageQuality = 85; // 0-100

  // File route configurations
  static const String productImagesRoute = 'productImages';
  static const String userAvatarsRoute = 'userAvatars';
  static const String postImagesRoute = 'postImages';

  // Get upload URL for specific route
  static String getUploadUrl(String route) {
    return '$baseUrl$uploadEndpoint?slug=$route';
  }

  // Get file URL from UploadThing response
  static String getFileUrl(String fileKey) {
    return 'https://utfs.io/f/$fileKey';
  }

  // Validate file type
  static bool isValidFileType(String mimeType) {
    return allowedFileTypes.contains(mimeType.toLowerCase());
  }

  // Validate file size
  static bool isValidFileSize(int sizeInBytes) {
    return sizeInBytes <= maxFileSize;
  }

  // Get file size in human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

// Instructions for setting up UploadThing:
/*
1. Sign up for UploadThing at https://uploadthing.com/
2. Create a new app in your dashboard
3. Copy your App ID and Secret from the dashboard
4. Create file routes in your UploadThing dashboard:
   - productImages: For product photos (max 5 files, 10MB each)
   - userAvatars: For user profile pictures (max 1 file, 5MB)
   - postImages: For community post images (max 3 files, 10MB each)
5. Replace the values above with your actual credentials
6. For production, use environment variables or secure storage

Example file route configuration in UploadThing dashboard:
{
  "productImages": {
    "maxFileSize": "10MB",
    "maxFileCount": 5,
    "allowedFileTypes": ["image"]
  },
  "userAvatars": {
    "maxFileSize": "5MB", 
    "maxFileCount": 1,
    "allowedFileTypes": ["image"]
  },
  "postImages": {
    "maxFileSize": "10MB",
    "maxFileCount": 3,
    "allowedFileTypes": ["image"]
  }
}
*/
