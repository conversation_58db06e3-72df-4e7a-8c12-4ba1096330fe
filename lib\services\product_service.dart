import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';
import '../services/firebase_service.dart';

/// Service class for handling product-related database operations
class ProductService {
  static final ProductService _instance = ProductService._internal();
  factory ProductService() => _instance;
  ProductService._internal();

  final FirebaseService _firebaseService = FirebaseService();

  /// Create a new product in Firestore
  Future<String> createProduct(Product product) async {
    try {
      final docRef = await _firebaseService.productsCollection.add(
        product.toFirestore(),
      );
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create product: $e');
    }
  }

  /// Get a product by ID
  Future<Product?> getProduct(String productId) async {
    try {
      final doc = await _firebaseService.productsCollection
          .doc(productId)
          .get();
      if (doc.exists) {
        return Product.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product: $e');
    }
  }

  /// Update an existing product
  Future<void> updateProduct(
    String productId,
    Map<String, dynamic> updates,
  ) async {
    try {
      updates['updatedAt'] = Timestamp.fromDate(DateTime.now());
      await _firebaseService.productsCollection.doc(productId).update(updates);
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  /// Delete a product (soft delete by setting isActive to false)
  Future<void> deleteProduct(String productId) async {
    try {
      await _firebaseService.productsCollection.doc(productId).update({
        'isActive': false,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  /// Get products by seller ID
  Future<List<Product>> getProductsBySeller(String sellerId) async {
    try {
      final querySnapshot = await _firebaseService.productsCollection
          .where('sellerId', isEqualTo: sellerId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get products by seller: $e');
    }
  }

  /// Get products by category
  Future<List<Product>> getProductsByCategory(
    String category, {
    int limit = 20,
  }) async {
    try {
      final querySnapshot = await _firebaseService.productsCollection
          .where('category', isEqualTo: category)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get products by category: $e');
    }
  }

  /// Search products by title or description
  Future<List<Product>> searchProducts(String query, {int limit = 20}) async {
    try {
      // Note: This is a basic search. For better search functionality,
      // consider using Algolia or implementing full-text search
      final querySnapshot = await _firebaseService.productsCollection
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit * 2) // Get more to filter locally
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();

      // Filter locally by title and description
      final filteredProducts = products
          .where((product) {
            final searchQuery = query.toLowerCase();
            return product.title.toLowerCase().contains(searchQuery) ||
                product.description.toLowerCase().contains(searchQuery) ||
                (product.brand?.toLowerCase().contains(searchQuery) ?? false);
          })
          .take(limit)
          .toList();

      return filteredProducts;
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }

  /// Get all active products with pagination
  Future<List<Product>> getAllProducts({
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firebaseService.productsCollection
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get products: $e');
    }
  }

  /// Increment product view count
  Future<void> incrementViewCount(String productId) async {
    try {
      await _firebaseService.productsCollection.doc(productId).update({
        'viewCount': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('Failed to increment view count: $e');
    }
  }

  /// Increment product favorite count
  Future<void> incrementFavoriteCount(String productId) async {
    try {
      await _firebaseService.productsCollection.doc(productId).update({
        'favoriteCount': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('Failed to increment favorite count: $e');
    }
  }

  /// Decrement product favorite count
  Future<void> decrementFavoriteCount(String productId) async {
    try {
      await _firebaseService.productsCollection.doc(productId).update({
        'favoriteCount': FieldValue.increment(-1),
      });
    } catch (e) {
      throw Exception('Failed to decrement favorite count: $e');
    }
  }

  /// Get products stream for real-time updates
  Stream<List<Product>> getProductsStream({
    String? category,
    String? sellerId,
    int limit = 20,
  }) {
    try {
      Query query = _firebaseService.productsCollection.where(
        'isActive',
        isEqualTo: true,
      );

      if (category != null) {
        query = query.where('category', isEqualTo: category);
      }

      if (sellerId != null) {
        query = query.where('sellerId', isEqualTo: sellerId);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      return query.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) => Product.fromFirestore(doc)).toList();
      });
    } catch (e) {
      throw Exception('Failed to get products stream: $e');
    }
  }

  /// Update product stock
  Future<void> updateStock(String productId, int newStock) async {
    try {
      await _firebaseService.productsCollection.doc(productId).update({
        'stock': newStock,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update stock: $e');
    }
  }

  /// Get featured products (high rating, recent, popular)
  Future<List<Product>> getFeaturedProducts({int limit = 10}) async {
    try {
      final querySnapshot = await _firebaseService.productsCollection
          .where('isActive', isEqualTo: true)
          .orderBy('favoriteCount', descending: true)
          .orderBy('viewCount', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get featured products: $e');
    }
  }

  /// Get products by price range
  Future<List<Product>> getProductsByPriceRange({
    required double minPrice,
    required double maxPrice,
    String? category,
    int limit = 20,
  }) async {
    try {
      Query query = _firebaseService.productsCollection
          .where('isActive', isEqualTo: true)
          .where('price', isGreaterThanOrEqualTo: minPrice)
          .where('price', isLessThanOrEqualTo: maxPrice);

      if (category != null) {
        query = query.where('category', isEqualTo: category);
      }

      query = query.orderBy('price').limit(limit);

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get products by price range: $e');
    }
  }

  /// Get product statistics for a seller
  Future<Map<String, dynamic>> getSellerStats(String sellerId) async {
    try {
      final products = await getProductsBySeller(sellerId);

      final totalProducts = products.length;
      final activeProducts = products.where((p) => p.isActive).length;
      final totalViews = products.fold<int>(
        0,
        (total, p) => total + p.viewCount,
      );
      final totalFavorites = products.fold<int>(
        0,
        (total, p) => total + p.favoriteCount,
      );
      final averagePrice = products.isNotEmpty
          ? products.fold<double>(0, (total, p) => total + p.price) /
                products.length
          : 0.0;

      return {
        'totalProducts': totalProducts,
        'activeProducts': activeProducts,
        'totalViews': totalViews,
        'totalFavorites': totalFavorites,
        'averagePrice': averagePrice,
      };
    } catch (e) {
      throw Exception('Failed to get seller stats: $e');
    }
  }

  /// Batch update multiple products
  Future<void> batchUpdateProducts(
    List<String> productIds,
    Map<String, dynamic> updates,
  ) async {
    try {
      final batch = _firebaseService.firestore.batch();
      updates['updatedAt'] = Timestamp.fromDate(DateTime.now());

      for (final productId in productIds) {
        final docRef = _firebaseService.productsCollection.doc(productId);
        batch.update(docRef, updates);
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to batch update products: $e');
    }
  }
}
