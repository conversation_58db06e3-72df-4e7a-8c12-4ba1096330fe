import 'dart:io';
import 'package:dio/dio.dart';
import 'package:image/image.dart' as img;
import '../config/uploadthing_config.dart';

class UploadThingService {
  final Dio _dio = Dio();

  /// Upload a single image to UploadThing with progress tracking
  Future<UploadThingResult> uploadImage(
    File imageFile, {
    Function(double)? onProgress,
    String route = 'productImages',
  }) async {
    try {
      // Validate file
      final fileSize = await imageFile.length();
      if (!UploadThingConfig.isValidFileSize(fileSize)) {
        return UploadThingResult(
          success: false,
          error:
              'File size too large. Max size: ${UploadThingConfig.formatFileSize(UploadThingConfig.maxFileSize)}',
        );
      }

      // Optimize image before upload
      final optimizedFile = await _optimizeImage(imageFile);

      // Step 1: Get presigned URL from UploadThing
      final presignedResponse = await _getPresignedUrl(route);
      if (!presignedResponse['success']) {
        return UploadThingResult(
          success: false,
          error: presignedResponse['error'],
        );
      }

      final presignedUrl = presignedResponse['presignedUrl'];
      final fileKey = presignedResponse['fileKey'];

      // Step 2: Upload file to presigned URL
      final fileName =
          'product_${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}';
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          optimizedFile.path,
          filename: fileName,
        ),
      });

      final response = await _dio.post(
        presignedUrl,
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            final progress = sent / total;
            onProgress(progress);
          }
        },
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          validateStatus: (status) => status! < 500,
        ),
      );

      // Clean up optimized file if it's different from original
      if (optimizedFile.path != imageFile.path) {
        await optimizedFile.delete();
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        return UploadThingResult(
          success: true,
          fileKey: fileKey,
          fileName: fileName,
          fileUrl: UploadThingConfig.getFileUrl(fileKey),
          fileSize: fileSize,
          fileType: 'image/jpeg',
        );
      } else {
        return UploadThingResult(
          success: false,
          error: 'Upload failed with status: ${response.statusCode}',
        );
      }
    } catch (e) {
      return UploadThingResult(success: false, error: 'Upload error: $e');
    }
  }

  /// Get presigned URL from UploadThing
  Future<Map<String, dynamic>> _getPresignedUrl(String route) async {
    try {
      final response = await _dio.post(
        '${UploadThingConfig.baseUrl}/api/uploadthing',
        data: {
          'slug': route,
          'files': [
            {
              'name': 'image.jpg',
              'size': 1024000, // 1MB placeholder
              'type': 'image/jpeg',
            },
          ],
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'X-Uploadthing-Api-Key': UploadThingConfig.secret,
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is List && data.isNotEmpty) {
          final fileData = data.first;
          return {
            'success': true,
            'presignedUrl': fileData['url'],
            'fileKey': fileData['key'],
          };
        }
      }

      return {'success': false, 'error': 'Failed to get presigned URL'};
    } catch (e) {
      return {'success': false, 'error': 'Error getting presigned URL: $e'};
    }
  }

  /// Upload multiple images with individual progress tracking
  Future<List<UploadThingResult>> uploadMultipleImages(
    List<File> imageFiles, {
    Function(int index, double progress)? onProgress,
    String route = 'productImages',
  }) async {
    final results = <UploadThingResult>[];

    for (int i = 0; i < imageFiles.length; i++) {
      final result = await uploadImage(
        imageFiles[i],
        onProgress: (progress) {
          onProgress?.call(i, progress);
        },
        route: route,
      );
      results.add(result);
    }

    return results;
  }

  /// Delete a file from UploadThing
  Future<bool> deleteFile(String fileKey) async {
    try {
      final response = await _dio.delete(
        '${UploadThingConfig.baseUrl}/api/deleteFile',
        data: {'fileKey': fileKey},
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'X-Uploadthing-Api-Key': UploadThingConfig.secret,
          },
        ),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting file: $e');
      return false;
    }
  }

  /// Optimize image before upload
  Future<File> _optimizeImage(File imageFile) async {
    try {
      // Read image
      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);

      if (image == null) return imageFile;

      // Resize if needed
      img.Image resizedImage = image;
      if (image.width > UploadThingConfig.maxImageWidth ||
          image.height > UploadThingConfig.maxImageHeight) {
        resizedImage = img.copyResize(
          image,
          width: image.width > image.height
              ? UploadThingConfig.maxImageWidth
              : null,
          height: image.height > image.width
              ? UploadThingConfig.maxImageHeight
              : null,
          maintainAspect: true,
        );
      }

      // Compress image
      final compressedBytes = img.encodeJpg(
        resizedImage,
        quality: UploadThingConfig.imageQuality,
      );

      // Create optimized file
      final optimizedFile = File('${imageFile.path}_optimized.jpg');
      await optimizedFile.writeAsBytes(compressedBytes);

      return optimizedFile;
    } catch (e) {
      print('Error optimizing image: $e');
      return imageFile; // Return original if optimization fails
    }
  }

  /// Get file info from UploadThing
  Future<Map<String, dynamic>?> getFileInfo(String fileKey) async {
    try {
      final response = await _dio.get(
        '${UploadThingConfig.baseUrl}/api/getFileInfo',
        queryParameters: {'fileKey': fileKey},
        options: Options(
          headers: {'X-Uploadthing-Api-Key': UploadThingConfig.secret},
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error getting file info: $e');
      return null;
    }
  }

  /// Batch delete multiple files
  Future<bool> deleteMultipleFiles(List<String> fileKeys) async {
    try {
      final response = await _dio.delete(
        '${UploadThingConfig.baseUrl}/api/deleteFiles',
        data: {'fileKeys': fileKeys},
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'X-Uploadthing-Api-Key': UploadThingConfig.secret,
          },
        ),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting files: $e');
      return false;
    }
  }

  /// Get usage statistics
  Future<Map<String, dynamic>?> getUsageStats() async {
    try {
      final response = await _dio.get(
        '${UploadThingConfig.baseUrl}/api/getUsage',
        options: Options(
          headers: {'X-Uploadthing-Api-Key': UploadThingConfig.secret},
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error getting usage stats: $e');
      return null;
    }
  }
}

class UploadThingResult {
  final bool success;
  final String? fileKey;
  final String? fileName;
  final String? fileUrl;
  final int? fileSize;
  final String? fileType;
  final String? error;

  UploadThingResult({
    required this.success,
    this.fileKey,
    this.fileName,
    this.fileUrl,
    this.fileSize,
    this.fileType,
    this.error,
  });

  @override
  String toString() {
    if (success) {
      return 'UploadThingResult(success: true, fileKey: $fileKey, url: $fileUrl)';
    } else {
      return 'UploadThingResult(success: false, error: $error)';
    }
  }
}
