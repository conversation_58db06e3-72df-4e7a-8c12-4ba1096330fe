import 'package:cloud_firestore/cloud_firestore.dart';

class Product {
  final String id;
  final String title;
  final String description;
  final double price;
  final String category;
  final String condition;
  final String? brand;
  final int stock;
  final List<String> images;
  final String sellerId;
  final String sellerName;
  final String? sellerAvatar;
  final String location;
  final bool isNegotiable;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final double? rating;
  final int viewCount;
  final int favoriteCount;
  final Map<String, dynamic>? metadata;

  const Product({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.category,
    required this.condition,
    this.brand,
    required this.stock,
    required this.images,
    required this.sellerId,
    required this.sellerName,
    this.sellerAvatar,
    required this.location,
    this.isNegotiable = false,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.rating,
    this.viewCount = 0,
    this.favoriteCount = 0,
    this.metadata,
  });

  factory Product.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Product(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      category: data['category'] ?? '',
      condition: data['condition'] ?? 'New',
      brand: data['brand'],
      stock: data['stock'] ?? 0,
      images: List<String>.from(data['images'] ?? []),
      sellerId: data['sellerId'] ?? '',
      sellerName: data['sellerName'] ?? '',
      sellerAvatar: data['sellerAvatar'],
      location: data['location'] ?? '',
      isNegotiable: data['isNegotiable'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
      rating: data['rating']?.toDouble(),
      viewCount: data['viewCount'] ?? 0,
      favoriteCount: data['favoriteCount'] ?? 0,
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'price': price,
      'category': category,
      'condition': condition,
      'brand': brand,
      'stock': stock,
      'images': images,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerAvatar': sellerAvatar,
      'location': location,
      'isNegotiable': isNegotiable,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
      'rating': rating,
      'viewCount': viewCount,
      'favoriteCount': favoriteCount,
      'metadata': metadata,
    };
  }

  Product copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? category,
    String? condition,
    String? brand,
    int? stock,
    List<String>? images,
    String? sellerId,
    String? sellerName,
    String? sellerAvatar,
    String? location,
    bool? isNegotiable,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    double? rating,
    int? viewCount,
    int? favoriteCount,
    Map<String, dynamic>? metadata,
  }) {
    return Product(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      condition: condition ?? this.condition,
      brand: brand ?? this.brand,
      stock: stock ?? this.stock,
      images: images ?? this.images,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      sellerAvatar: sellerAvatar ?? this.sellerAvatar,
      location: location ?? this.location,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      rating: rating ?? this.rating,
      viewCount: viewCount ?? this.viewCount,
      favoriteCount: favoriteCount ?? this.favoriteCount,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'Product(id: $id, title: $title, price: $price, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
