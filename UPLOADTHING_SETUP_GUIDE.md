# UploadThing Setup Guide for dekuMart

## 🎯 Overview
This guide will help you set up UploadThing as the image upload service for your dekuMart app, replacing Cloudinary.

## 📋 Prerequisites
- UploadThing account (free tier available)
- Flutter project with the updated dependencies

## 🚀 Step 1: Create UploadThing Account

1. Go to [UploadThing.com](https://uploadthing.com/)
2. Sign up for a free account
3. Verify your email address
4. Log in to your dashboard

## 🔧 Step 2: Create a New App

1. In your UploadThing dashboard, click **"Create App"**
2. Enter app name: `dekumart-app`
3. Choose your preferred region (closest to your users)
4. Click **"Create App"**

## 🔑 Step 3: Get Your API Keys

1. In your app dashboard, go to **"API Keys"** tab
2. Copy your **App ID** (starts with `app_`)
3. Copy your **Secret Key** (starts with `sk_live_` or `sk_test_`)
4. Keep these secure - you'll need them in the next step

## ⚙️ Step 4: Configure Your Flutter App

### Update Configuration File
Open `lib/config/uploadthing_config.dart` and replace the placeholder values:

```dart
class UploadThingConfig {
  // Replace with your actual UploadThing credentials
  static const String appId = 'app_your_actual_app_id_here';
  static const String secret = 'sk_live_your_actual_secret_key_here';
  
  // Rest of the configuration remains the same...
}
```

### Environment Variables (Recommended for Production)
For better security, use environment variables:

1. Create a `.env` file in your project root:
```env
UPLOADTHING_APP_ID=app_your_actual_app_id_here
UPLOADTHING_SECRET=sk_live_your_actual_secret_key_here
```

2. Add `.env` to your `.gitignore` file
3. Use a package like `flutter_dotenv` to load environment variables

## 📁 Step 5: Configure File Routes

In your UploadThing dashboard:

1. Go to **"File Routes"** tab
2. Create the following routes:

### Product Images Route
```json
{
  "slug": "productImages",
  "config": {
    "image": {
      "maxFileSize": "10MB",
      "maxFileCount": 5
    }
  }
}
```

### User Avatars Route
```json
{
  "slug": "userAvatars", 
  "config": {
    "image": {
      "maxFileSize": "5MB",
      "maxFileCount": 1
    }
  }
}
```

### Post Images Route
```json
{
  "slug": "postImages",
  "config": {
    "image": {
      "maxFileSize": "10MB", 
      "maxFileCount": 3
    }
  }
}
```

## 🔒 Step 6: Set Up Security (Optional but Recommended)

### CORS Configuration
1. In your UploadThing dashboard, go to **"Settings"** > **"CORS"**
2. Add your app domains:
   - `localhost:*` (for development)
   - Your production domain (when deployed)

### Webhook Configuration (Optional)
1. Go to **"Webhooks"** tab
2. Add webhook URL: `https://your-app-domain.com/api/uploadthing-webhook`
3. Select events: `upload.completed`, `upload.failed`

## 🧪 Step 7: Test the Integration

### Run the App
```bash
flutter pub get
flutter run
```

### Test Image Upload
1. Open the app and navigate to "Post Product"
2. Try adding product images
3. Check if images upload successfully
4. Verify images appear in your UploadThing dashboard

## 📊 Step 8: Monitor Usage

### Dashboard Monitoring
- Check upload statistics in your UploadThing dashboard
- Monitor storage usage and bandwidth
- Review error logs if uploads fail

### Free Tier Limits
- **Storage**: 2GB
- **Bandwidth**: 1GB/month
- **File Size**: 16MB max per file
- **Uploads**: Unlimited

## 🔧 Troubleshooting

### Common Issues

#### 1. Upload Fails with 401 Error
- **Cause**: Invalid API key
- **Solution**: Double-check your App ID and Secret Key

#### 2. Upload Fails with 403 Error
- **Cause**: File route not configured or CORS issue
- **Solution**: Verify file routes in dashboard and CORS settings

#### 3. File Size Too Large
- **Cause**: File exceeds route limits
- **Solution**: Check file route configuration and image optimization

#### 4. Network Timeout
- **Cause**: Slow internet or large files
- **Solution**: Implement retry logic and better progress feedback

### Debug Mode
Enable debug logging in your app:

```dart
// In your upload service
print('Upload URL: $uploadUrl');
print('File size: ${await imageFile.length()} bytes');
print('Response: ${response.data}');
```

## 🚀 Production Deployment

### Security Checklist
- [ ] Use environment variables for API keys
- [ ] Enable CORS for your production domain
- [ ] Set up proper error handling
- [ ] Implement retry logic for failed uploads
- [ ] Add upload progress indicators
- [ ] Test with various file sizes and types

### Performance Optimization
- [ ] Implement image compression before upload
- [ ] Use appropriate file routes for different use cases
- [ ] Cache uploaded file URLs
- [ ] Implement lazy loading for image galleries

## 📈 Scaling Considerations

### Paid Plans
When you outgrow the free tier:
- **Pro Plan**: $20/month - 100GB storage, 1TB bandwidth
- **Team Plan**: $100/month - 1TB storage, 5TB bandwidth
- **Enterprise**: Custom pricing for larger needs

### CDN Benefits
UploadThing automatically provides:
- Global CDN distribution
- Automatic image optimization
- WebP conversion
- Responsive image serving

## 🎉 You're All Set!

Your dekuMart app now uses UploadThing for image uploads! The service provides:

✅ **Fast, reliable uploads**
✅ **Automatic image optimization**
✅ **Global CDN delivery**
✅ **Built-in security features**
✅ **Generous free tier**
✅ **Simple API integration**

## 📞 Support

If you encounter issues:
1. Check the [UploadThing Documentation](https://docs.uploadthing.com/)
2. Visit the [UploadThing Discord](https://discord.gg/uploadthing)
3. Contact UploadThing support through their dashboard

## 🔄 Migration from Cloudinary

The migration is complete! Key differences:
- **Cloudinary**: `result.secureUrl` → **UploadThing**: `result.fileUrl`
- **Cloudinary**: Folder-based organization → **UploadThing**: Route-based organization
- **Cloudinary**: Manual transformations → **UploadThing**: Automatic optimizations

Your app will now use UploadThing for all new image uploads while maintaining the same user experience.
