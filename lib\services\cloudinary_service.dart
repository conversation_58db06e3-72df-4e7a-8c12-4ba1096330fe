import 'dart:io';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import '../config/cloudinary_config.dart';

class CloudinaryService {
  static String get _cloudName => CloudinaryConfig.cloudName;
  static String get _apiKey => CloudinaryConfig.apiKey;
  static String get _apiSecret => CloudinaryConfig.apiSecret;
  static String get _uploadPreset => CloudinaryConfig.uploadPreset;

  final Dio _dio = Dio();

  /// Upload a single image to Cloudinary with progress tracking
  Future<CloudinaryUploadResult> uploadImage(
    File imageFile, {
    Function(double)? onProgress,
    String? folder,
  }) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

      // Create form data
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'product_${timestamp}_${imageFile.path.split('/').last}',
        ),
        'upload_preset': _uploadPreset,
        'timestamp': timestamp,
        'folder': folder ?? CloudinaryConfig.defaultFolder,
        'resource_type': 'image',
        'quality': 'auto:good',
        'format': 'auto',
        'transformation':
            'w_${CloudinaryConfig.maxImageSize},h_${CloudinaryConfig.maxImageSize},c_limit,q_${CloudinaryConfig.imageQuality}',
      });

      // Upload URL
      final uploadUrl =
          'https://api.cloudinary.com/v1_1/$_cloudName/image/upload';

      // Make the upload request with progress tracking
      final response = await _dio.post(
        uploadUrl,
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            final progress = sent / total;
            onProgress(progress);
          }
        },
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return CloudinaryUploadResult(
          success: true,
          publicId: data['public_id'],
          secureUrl: data['secure_url'],
          url: data['url'],
          width: data['width'],
          height: data['height'],
          format: data['format'],
          bytes: data['bytes'],
        );
      } else {
        return CloudinaryUploadResult(
          success: false,
          error: 'Upload failed with status: ${response.statusCode}',
        );
      }
    } catch (e) {
      return CloudinaryUploadResult(success: false, error: 'Upload error: $e');
    }
  }

  /// Upload multiple images with individual progress tracking
  Future<List<CloudinaryUploadResult>> uploadMultipleImages(
    List<File> imageFiles, {
    Function(int index, double progress)? onProgress,
    String? folder,
  }) async {
    final results = <CloudinaryUploadResult>[];

    for (int i = 0; i < imageFiles.length; i++) {
      final result = await uploadImage(
        imageFiles[i],
        onProgress: (progress) {
          onProgress?.call(i, progress);
        },
        folder: folder,
      );
      results.add(result);
    }

    return results;
  }

  /// Delete an image from Cloudinary
  Future<bool> deleteImage(String publicId) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final signature = _generateSignature({
        'public_id': publicId,
        'timestamp': timestamp,
      });

      final formData = FormData.fromMap({
        'public_id': publicId,
        'timestamp': timestamp,
        'api_key': _apiKey,
        'signature': signature,
      });

      final deleteUrl =
          'https://api.cloudinary.com/v1_1/$_cloudName/image/destroy';

      final response = await _dio.post(deleteUrl, data: formData);

      return response.statusCode == 200 && response.data['result'] == 'ok';
    } catch (e) {
      return false;
    }
  }

  /// Generate signature for authenticated requests
  String _generateSignature(Map<String, String> params) {
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    final paramString = sortedParams.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');

    final stringToSign = '$paramString$_apiSecret';
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);

    return digest.toString();
  }

  /// Get optimized image URL with transformations
  String getOptimizedImageUrl(
    String publicId, {
    int? width,
    int? height,
    String quality = 'auto:good',
    String format = 'auto',
  }) {
    final transformations = <String>[];

    if (width != null) transformations.add('w_$width');
    if (height != null) transformations.add('h_$height');
    transformations.add('q_$quality');
    transformations.add('f_$format');

    final transformString = transformations.join(',');
    return 'https://res.cloudinary.com/$_cloudName/image/upload/$transformString/$publicId';
  }
}

class CloudinaryUploadResult {
  final bool success;
  final String? publicId;
  final String? secureUrl;
  final String? url;
  final int? width;
  final int? height;
  final String? format;
  final int? bytes;
  final String? error;

  CloudinaryUploadResult({
    required this.success,
    this.publicId,
    this.secureUrl,
    this.url,
    this.width,
    this.height,
    this.format,
    this.bytes,
    this.error,
  });

  @override
  String toString() {
    if (success) {
      return 'CloudinaryUploadResult(success: true, publicId: $publicId, url: $secureUrl)';
    } else {
      return 'CloudinaryUploadResult(success: false, error: $error)';
    }
  }
}
